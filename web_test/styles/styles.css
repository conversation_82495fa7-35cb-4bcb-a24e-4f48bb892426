#main-sidebar-wrapper button[data-sidebar="menu-button"]:hover,
#main-topbar-wrapper button:hover:not(.tab-close-btn) {
  background-color: hsl(var(--sidebar-accent));
}

#main-topbar-wrapper {
  border-bottom: 1px solid hsl(var(--sidebar-border));
}

[role="tab"] {
  color: hsl(var(--foreground));
  transition: all 0.2s ease;
  position: relative;
  font-size: 0.9rem;
  border-bottom: 2px solid transparent;
}

[role="tab"]:hover {
  background-color: hsl(var(--muted) / 0.5);
}

[role="tab"][data-state="active"] {
  border-bottom-color: hsl(var(--primary));
  font-weight: 500;
}

.tab-close-btn {
  opacity: 0.6;
  transition: all 0.2s ease;
}

.tab-close-btn:hover {
  opacity: 1;
  background-color: hsl(var(--muted));
}

.chart-container {
  max-height: 300px;
  min-height: 200px;
}

#chart-price-history {
  height: 300px;
  width: 100%;
}

@media (max-width: 768px) {
  .chart-container {
    max-width: 100vw;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
  }
}

@media (max-width: 640px) {
  #main-sidebar-wrapper {
    display: none;
  }
  .chart-container {
    max-width: 100vw;
    min-width: 0;
    width: 100%;
    height: auto;
    min-height: 180px;
    max-height: 220px;
  }
  #stockChart {
    width: 100vw !important;
    min-width: 0 !important;
    height: 220px !important;
    max-height: 220px !important;
  }
  #allIndicators .chart-container {
    min-height: 120px;
    max-height: 160px;
    height: 140px;
  }
  #allIndicators canvas {
    height: 120px !important;
    max-height: 140px !important;
  }
}

/* Chart Section Styles */
.chart-section {
  background-color: hsl(var(--card));
  border-radius: 0.5rem;
  overflow: hidden;
}

.chart-section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  background-color: hsl(var(--muted) / 0.3);
  border-bottom: 1px solid hsl(var(--border));
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.chart-section-header:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.chart-section-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--foreground));
}

.chart-section-icon {
  color: hsl(var(--muted-foreground));
}

.chart-section-toggle {
  padding: 0.25rem;
  border-radius: 0.375rem;
  transition: background-color 0.2s ease;
}

.chart-section-toggle:hover {
  background-color: hsl(var(--background) / 0.8);
}

.chart-section-chevron {
  transition: transform 0.2s ease;
}

.chart-section.collapsed .chart-section-chevron {
  transform: rotate(-90deg);
}

.chart-section-content {
  padding: 1rem;
}

.chart-section.collapsed .chart-section-content {
  display: none;
}

/* Stock Signals Sidebar */
#stockSignalsSidebar {
  flex-shrink: 0;
  transition: width 0.3s ease, transform 0.3s ease;
  min-width: 250px;
  max-width: 500px;
}

#stockSignalsSidebar.collapsed {
  width: 0 !important;
  min-width: 0;
  overflow: hidden;
  border-left: none;
}

#sidebarResizer {
  transition: background-color 0.2s ease;
}

#sidebarResizer:hover {
  background-color: hsl(var(--primary));
}

#sidebarToggle svg {
  transition: transform 0.3s ease;
}

/* Signal Cards */
.signal-card {
  background-color: hsl(var(--background));
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.signal-card.buy {
  border-color: #bbf7d0;
  background-color: #f0fdf4;
}

.signal-card.sell {
  border-color: #fecaca;
  background-color: #fef2f2;
}

.signal-card.neutral {
  border-color: #fde68a;
  background-color: #fffbeb;
}

.signal-value {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.875rem;
}

.signal-recommendation {
  font-size: 0.75rem;
  color: hsl(var(--muted-foreground));
  margin-top: 0.25rem;
}

/* Trading Zone Cards */
.trading-zone {
  background-color: hsl(var(--background));
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.trading-zone.support {
  border-color: #bbf7d0;
  background-color: #f0fdf4;
}

.trading-zone.resistance {
  border-color: #fecaca;
  background-color: #fef2f2;
}

.zone-price {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-weight: 600;
}

.zone-confidence {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.zone-confidence.high {
  background-color: #dcfce7;
  color: #166534;
}

.zone-confidence.medium {
  background-color: #fef3c7;
  color: #92400e;
}

.zone-confidence.low {
  background-color: #f3f4f6;
  color: #374151;
}

/* Risk/Reward Cards */
.rr-card {
  background-color: hsl(var(--background));
  border-radius: 0.5rem;
  padding: 0.75rem;
}

.rr-ratio {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-weight: 700;
  font-size: 1.125rem;
}

.rr-quality {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.rr-quality.excellent {
  background-color: #dcfce7;
  color: #166534;
}

.rr-quality.good {
  background-color: #dbeafe;
  color: #1e40af;
}

.rr-quality.fair {
  background-color: #fef3c7;
  color: #92400e;
}

/* Responsive adjustments for new layout */
@media (max-width: 1024px) {
  #stockSignalsSidebar {
    width: 18rem;
  }
}

@media (max-width: 768px) {
  #stockDetailView {
    flex-direction: column;
  }

  #stockSignalsSidebar {
    width: 100%;
    border-left: none;
    border-top: 1px solid hsl(var(--border));
  }
}

/* Enhanced Watchlist Styles */
.stock-row {
  transition: all 0.2s ease;
}

.stock-row:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stock-row .group-hover\:border-primary\/40:hover {
  border-color: hsl(var(--primary) / 0.4);
}

/* Gradient backgrounds for better visual hierarchy */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-primary\/20 {
  --tw-gradient-from: hsl(var(--primary) / 0.2);
  --tw-gradient-to: hsl(var(--primary) / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-primary\/10 {
  --tw-gradient-to: hsl(var(--primary) / 0.1);
}

.from-blue-100 {
  --tw-gradient-from: #dbeafe;
  --tw-gradient-to: rgb(219 234 254 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-blue-200 {
  --tw-gradient-to: #bfdbfe;
}

.from-purple-100 {
  --tw-gradient-from: #f3e8ff;
  --tw-gradient-to: rgb(243 232 255 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-purple-200 {
  --tw-gradient-to: #e9d5ff;
}

.from-yellow-100 {
  --tw-gradient-from: #fef3c7;
  --tw-gradient-to: rgb(254 243 199 / 0);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-orange-100 {
  --tw-gradient-to: #ffedd5;
}

/* Enhanced table styling */
.watchlist-table thead th {
  position: sticky;
  top: 0;
  z-index: 10;
  background: hsl(var(--muted) / 0.3);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

/* Improved spacing and typography */
.leading-relaxed {
  line-height: 1.625;
}

/* Animation for loading states */
@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.95);
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
