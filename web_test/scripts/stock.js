const ANALYSIS_PATH = 'analysis/';
const MAIN_SUFFIX = '.json';
const INDICATOR_LABELS = {
    rsi: 'RSI',
    macd: 'MACD',
    ma: 'Moving Average',
    adx: 'ADX',
    cci: 'CCI',
    bb: 'Bollinger Bands',
    obv: 'OBV',
    roc: 'ROC',
    ultosc: 'Ultimate Oscillator',
    sar: 'Parabolic SAR',
    momentum: 'Momentum',
    ao: 'Awesome Oscillator',
    bearpower: 'Bear Power',
    stochastic: 'Stochastic',
    stochrsi: 'StochRSI',
    ichimoku: 'Ichimoku',
    pp: 'Pivot Points',
    tsf: 'TSF',
    adi: 'ADI',
    wpr: 'Williams %R',
    rsw: 'RS(52W)'
};

// Chart categorization for organized display
const CHART_CATEGORIES = {
    'price-analysis': ['ma', 'bb', 'pp'],
    'momentum': ['rsi', 'macd', 'stochastic', 'stochrsi', 'cci', 'wpr', 'ultosc', 'roc', 'momentum'],
    'volume': ['obv', 'adi'],
    'trend': ['sar', 'adx', 'ao', 'bearpower', 'ichimoku', 'tsf', 'rsw']
};

// Collapsible sections state storage key
const SECTIONS_STATE_KEY = 'chartSectionsState';

const chart1Color = 'hsl(173, 58%, 39%)'
const chart2Color = 'hsl(12, 76%, 61%)'

// Export priceChart instance to be managed by script.js if needed
export let priceChartInstance = null;

const mainDataCache = {};
const priceHistoryCache = {};
const indicatorDataCache = {}; // { symbol: { indicator: data } }

function fetchJSONWithCache(path, cache) {
    if (cache[path]) {
        return Promise.resolve(cache[path]);
    }
    return fetch(path)
        .then(res => res.json())
        .then(data => {
            cache[path] = data;
            return data;
        });
}

export function clearAllIndicatorsGlobally() {
    // Destroy all indicator chart instances
    for (const key in indicatorCharts) {
        if (indicatorCharts[key]) {
            indicatorCharts[key].destroy();
            delete indicatorCharts[key];
        }
    }
    const container = document.getElementById('allIndicators');
    if (container) container.innerHTML = '';
}

export function clearStockViewContent() {
    if (priceChartInstance) {
        priceChartInstance.remove();
        priceChartInstance = null;
    }
    const stockHeader = document.getElementById('stockHeader');
    if (stockHeader) stockHeader.innerHTML = '';
    const summaryDiv = document.getElementById('analysisSummary');
    if (summaryDiv) summaryDiv.innerHTML = '';
    const sidebar = document.getElementById('stockSignalsSidebar');
    if (sidebar) {
        const content = sidebar.querySelector('.p-4.space-y-4');
        if (content) content.innerHTML = '';
    }
    clearAllIndicatorsGlobally();
}

export async function loadAndRenderSymbol(symbol) {
    if (!symbol || symbol === 'Watchlist') { // Guard against invalid symbol or "Watchlist"
        clearStockViewContent(); // Clear view if no valid symbol
        return;
    }
    const mainPath = ANALYSIS_PATH + symbol + MAIN_SUFFIX;
    const priceHistoryPath = ANALYSIS_PATH + symbol + '_prices.json';

    const mainData = await fetchJSONWithCache(mainPath, mainDataCache);

    // Render new layout components
    renderStockHeader(mainData);
    renderAnalysisSummary(mainData);
    renderStockSignalsSidebar(mainData);

    let priceHistory = null;
    try {
        priceHistory = await fetchJSONWithCache(priceHistoryPath, priceHistoryCache);
    } catch (e) {
        priceHistory = null;
    }
    renderStockChartFromHistory(priceHistory);
    renderCategorizedIndicators(symbol);
    initializeCollapsibleSections();
}

// New function to render stock header
function renderStockHeader(data) {
    const div = document.getElementById('stockHeader');
    const changeClass = data.p.cp >= 0 ? 'text-green-600' : 'text-red-600';
    const changeIcon = data.p.cp >= 0 ? '↗' : '↘';

    div.innerHTML = `
        <div class="bg-card border border-border rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center gap-4">
                    <h1 class="text-3xl font-bold text-foreground">${data.s}</h1>
                    <div class="text-2xl font-semibold text-foreground">${data.p.c.toLocaleString()}</div>
                    <div class="flex items-center gap-1 ${changeClass}">
                        <span class="text-lg">${changeIcon}</span>
                        <span class="text-lg font-medium">${data.p.cv} (${data.p.cp}%)</span>
                    </div>
                </div>
                <div class="text-sm text-muted-foreground">
                    Cập nhật: ${data.ltd}
                </div>
            </div>
            <div class="flex items-center gap-4 text-sm">
                <span class="px-3 py-1 rounded-full bg-muted text-muted-foreground">
                    Xu hướng: ${data.t.d} (${data.t.s})
                </span>
                <span class="px-3 py-1 rounded-full bg-muted text-muted-foreground">
                    Thị trường: ${data.mc}
                </span>
                <span class="px-3 py-1 rounded-full bg-muted text-muted-foreground">
                    Độ tin cậy: ${data.t.c}
                </span>
            </div>
        </div>
    `;
}

function renderAnalysisSummary(data) {
    const div = document.getElementById('analysisSummary');
    div.innerHTML = `
        <div class="bg-card border border-border rounded-lg p-4">
            <h2 class="text-lg font-semibold mb-3 text-foreground">Tóm tắt phân tích kỹ thuật</h2>
            <div class="prose prose-sm max-w-none text-foreground">
                ${data.r.s.replace(/\n/g, '<br>')}
            </div>
            ${data.ma && data.ma.length > 0 ? `
                <div class="mt-4 grid grid-cols-2 md:grid-cols-3 gap-2">
                    ${data.ma.map(ma => `
                        <div class="border border-border rounded p-2 bg-background">
                            <div class="font-medium text-sm">${ma.t} ${ma.p}</div>
                            <div class="text-xs text-muted-foreground">${ma.v} [${ma.s}]</div>
                        </div>
                    `).join('')}
                </div>
            ` : ''}
        </div>
    `;
}

/**
 * Render the stock price and volume chart using Lightweight Charts.
 * @param {Object} priceHistory - The price history object with t, o, h, l, c, v arrays.
 */
function renderStockChartFromHistory(priceHistory) {
    if (!priceHistory || !priceHistory.c || priceHistory.c.length === 0) {
        const container = document.getElementById('chart-price-history');
        container.innerHTML = '<div style="text-align:center;color:#888;padding:100px 0;">No price history data available</div>';
        return;
    }
    // Prepare data for Lightweight Charts
    const container = document.getElementById('chart-price-history');
    container.innerHTML = '';
    if (priceChartInstance) {
        priceChartInstance.remove();
        priceChartInstance = null;
    }

    priceChartInstance = LightweightCharts.createChart(container, {
        autoSize: true,
        width: container.clientWidth || 900,
        height: container.clientHeight || 300,
        layout: {
            backgroundColor: "#ffffff",
            textColor: "#333",
            attributionLogo: false,
            panes: {
                separatorColor: "#F3F4F6",
                separatorHoverColor: "#F3F4F6",
                enableResize: true,
            },
        },
        grid: {
            vertLines: { color: "#FBFBFC" },
            horzLines: { color: "#FBFBFC" },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: "#F3F4F6",
            scaleMargins: {
                top: 0.3, // leave some space for the legend
                bottom: 0.25,
            },
        },
        timeScale: {
            borderColor: "#F3F4F6",
            timeVisible: true,
            secondsVisible: false,
        },
    });

    // Candlestick data
    const candlestickSeries = priceChartInstance.addSeries(
        LightweightCharts.CandlestickSeries,
        {
            upColor: "hsl(173, 58%, 39%)",
            downColor: "hsl(12,76%, 61%)",
            borderVisible: false,
            wickUpColor: "hsl(173, 58%, 39%)",
            wickDownColor: "hsl(12,76%, 61%)",
        }
    );
    const candleData = priceHistory.c.map((close, i) => ({
        time: priceHistory.t[i],
        open: priceHistory.o[i],
        high: priceHistory.h[i],
        low: priceHistory.l[i],
        close: close,
    }));

    candlestickSeries.setData(candleData);

    // Volume data
    const volumeSeries = priceChartInstance.addSeries(
        LightweightCharts.HistogramSeries,
        {
            priceFormat: {
                type: "volume"
            }
        },
        1
    );
    const volumeData = priceHistory.v.map((v, i) => ({
        time: priceHistory.t[i],
        value: v,
        color: candleData[i].close > candleData[i].open ? 'hsl(173, 58%, 39%)' : 'hsl(12, 76%, 61%)',
    }));
    volumeSeries.setData(volumeData);
    volumeSeries.priceScale().applyOptions({
        autoScale: true,
        scaleMargins: {
            top: 0.1, // highest point of the series will be 70% away from the top
            bottom: 0,
        },
    });
}


const indicatorCharts = {};

function getColorForMA(key) {
    const colors = ['#1976d2', '#388e3c', '#fbc02d', '#d32f2f', '#7b1fa2', '#0288d1', '#c2185b', '#ff9800'];
    // s5, e5, s10, e10, ...
    const num = parseInt(key.match(/\d+/)?.[0] || '0');
    return colors[num % colors.length];
}
function getColorForIchimoku(key) {
    const colorMap = {
        tk: '#1976d2', // Tenkan
        kj: '#388e3c', // Kijun
        sa: '#fbc02d', // Senkou A
        sb: '#d32f2f', // Senkou B
        cs: '#7b1fa2', // Chikou
    };
    return colorMap[key] || '#0288d1';
}
function getColorForPP(key) {
    const colorMap = {
        pivot: '#1976d2',
        r1: '#388e3c',
        r2: '#fbc02d',
        r3: '#0288d1',
        s1: '#d32f2f',
        s2: '#7b1fa2',
        s3: '#c2185b',
    };
    return colorMap[key] || '#888';
}

async function renderAllIndicators(symbol) {
    clearAllIndicatorsGlobally();
    const container = document.getElementById('allIndicators');
    const isMobile = window.innerWidth <= 640;
    if (!indicatorDataCache[symbol]) indicatorDataCache[symbol] = {};
    for (const key of Object.keys(INDICATOR_LABELS)) {
        const indicatorPath = ANALYSIS_PATH + symbol + '_' + key + MAIN_SUFFIX;
        try {
            let indicatorData;
            if (indicatorDataCache[symbol][key]) {
                indicatorData = indicatorDataCache[symbol][key];
            } else {
                indicatorData = await fetchJSONWithCache(indicatorPath, {});
                indicatorDataCache[symbol][key] = indicatorData;
            }
            // Create a card for each indicator
            const card = document.createElement('div');
            card.className = 'chart-container bg-white rounded shadow flex flex-col mb-12';
            const title = document.createElement('div');
            title.className = 'font-semibold mb-2 text-center';
            title.textContent = (INDICATOR_LABELS[key] || key) + ' (Weekly)';
            const canvas = document.createElement('canvas');
            // Set canvas size for indicator charts (not close price)
            if (isMobile) {
                canvas.style.width = '100%';
                canvas.style.height = '300px';
            } else {
                canvas.style.width = '100%';
                canvas.style.height = '300px';
            }
            card.appendChild(title);
            card.appendChild(canvas);
            container.appendChild(card);
            // Render chart
            renderIndicatorChartToCanvas(key, indicatorData, canvas, key);
        } catch (e) {
            // No data for this indicator, skip
        }
    }
}

function renderIndicatorChartToCanvas(indicator, data, canvas, chartKey) {
    if (indicatorCharts[chartKey]) {
        indicatorCharts[chartKey].destroy();
    }
    let labels = [];
    let datasets = [];
    if (data.t && Array.isArray(data.t)) {
        labels = data.t.map(ts => new Date(ts * 1000).toLocaleDateString());
    }
    if (indicator === 'ma') {
        // Render all s* and e* keys as separate lines
        Object.keys(data).forEach(key => {
            if ((/^s\d+$/i.test(key) || /^e\d+$/i.test(key)) && Array.isArray(data[key])) {
                datasets.push({
                    label: key.toUpperCase(),
                    data: data[key],
                    borderColor: getColorForMA(key),
                    backgroundColor: 'rgba(0,0,0,0)',
                    fill: false,
                    tension: 0.1,
                });
            }
        });
    } else if (indicator === 'ichimoku') {
        const ichimokuLines = {
            tk: 'Tenkan',
            kj: 'Kijun',
            sa: 'Senkou A',
            sb: 'Senkou B',
            cs: 'Chikou'
        };
        Object.entries(ichimokuLines).forEach(([key, label]) => {
            if (data[key]) {
                datasets.push({
                    label,
                    data: data[key],
                    borderColor: getColorForIchimoku(key),
                    backgroundColor: 'rgba(0,0,0,0)',
                    fill: false,
                    tension: 0.1,
                });
            }
        });
    } else if (indicator === 'pp') {
        // Render all pivot, r1, r2, r3, s1, s2, s3
        ['pivot', 'r1', 'r2', 'r3', 's1', 's2', 's3'].forEach(key => {
            if (data[key]) {
                datasets.push({
                    label: key.toUpperCase(),
                    data: data[key],
                    borderColor: getColorForPP(key),
                    backgroundColor: 'rgba(0,0,0,0)',
                    fill: false,
                    tension: 0.1,
                });
            }
        });
    } else {
        // Default: v, k, d, m, u, l
        if (data.v) {
            datasets.push({
                label: INDICATOR_LABELS[indicator] || indicator,
                data: data.v,
                borderColor: 'rgba(75,192,192,1)',
                backgroundColor: 'rgba(75,192,192,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.k) {
            datasets.push({
                label: 'K',
                data: data.k,
                borderColor: 'rgba(255,99,132,1)',
                backgroundColor: 'rgba(255,99,132,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.d) {
            datasets.push({
                label: 'D',
                data: data.d,
                borderColor: 'rgba(54,162,235,1)',
                backgroundColor: 'rgba(54,162,235,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.m) {
            datasets.push({
                label: 'M',
                data: data.m,
                borderColor: 'rgba(255,206,86,1)',
                backgroundColor: 'rgba(255,206,86,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.u) {
            datasets.push({
                label: 'Upper',
                data: data.u,
                borderColor: 'rgba(153,102,255,1)',
                backgroundColor: 'rgba(153,102,255,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
        if (data.l) {
            datasets.push({
                label: 'Lower',
                data: data.l,
                borderColor: 'rgba(255,159,64,1)',
                backgroundColor: 'rgba(255,159,64,0.2)',
                fill: false,
                tension: 0.1,
            });
        }
    }
    indicatorCharts[chartKey] = new Chart(canvas.getContext('2d'), {
        type: 'line',
        data: { labels, datasets },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' },
                title: { display: false },
            },
            scales: {
                y: { beginAtZero: false },
                x: { ticks: { maxTicksLimit: 8, autoSkip: true } }
            }
        }
    });
}

// New function to render the stock signals sidebar
function renderStockSignalsSidebar(data) {
    const overallRec = document.getElementById('overallRecommendation');
    const keyIndicators = document.getElementById('keyIndicators');
    const tradingZones = document.getElementById('tradingZones');
    const riskRewardRatios = document.getElementById('riskRewardRatios');

    // Overall recommendation
    const recClass = data.r.r === 'Mua' ? 'buy' : data.r.r === 'Bán' ? 'sell' : 'neutral';
    overallRec.innerHTML = `
        <div class="text-center">
            <h3 class="font-semibold text-lg mb-2">Khuyến nghị tổng thể</h3>
            <div class="signal-card ${recClass} text-center">
                <div class="text-2xl font-bold">${data.r.r}</div>
                <div class="text-sm mt-1">Dựa trên phân tích kỹ thuật</div>
            </div>
        </div>
    `;

    // Key technical indicators
    const keyTechnicalIndicators = data.ti.slice(0, 6); // Show top 6 indicators
    keyIndicators.innerHTML = `
        <h4 class="font-semibold mb-3">Chỉ báo chính</h4>
        ${keyTechnicalIndicators.map(indicator => {
            if (!indicator.v && indicator.v !== 0) return '';
            const signalClass = indicator.s === 'Mua' ? 'buy' : indicator.s === 'Bán' ? 'sell' : 'neutral';
            return `
                <div class="signal-card ${signalClass}">
                    <div class="flex justify-between items-start mb-1">
                        <span class="font-medium text-sm">${indicator.n}</span>
                        <span class="signal-value">${typeof indicator.v === 'number' ? indicator.v.toFixed(2) : indicator.v}</span>
                    </div>
                    <div class="text-xs font-medium">${indicator.s}</div>
                    <div class="signal-recommendation">${indicator.a}</div>
                </div>
            `;
        }).join('')}
    `;

    // Buy/Sell zones
    const allZones = [...(data.bz || []), ...(data.slz || []), ...(data.tpz || [])];
    tradingZones.innerHTML = `
        <h4 class="font-semibold mb-3">Vùng giao dịch</h4>
        ${allZones.map(zone => {
            const zoneType = data.bz && data.bz.includes(zone) ? 'support' : 'resistance';
            const confidenceClass = zone.c === 'cao' ? 'high' : zone.c === 'trung bình' ? 'medium' : 'low';
            return `
                <div class="trading-zone ${zoneType}">
                    <div class="flex justify-between items-start mb-1">
                        <span class="zone-price">${zone.p}</span>
                        <span class="zone-confidence ${confidenceClass}">${zone.c}</span>
                    </div>
                    <div class="text-xs text-muted-foreground">${zone.r}</div>
                </div>
            `;
        }).join('')}
    `;

    // Risk/Reward ratios
    riskRewardRatios.innerHTML = `
        <h4 class="font-semibold mb-3">Tỷ lệ Risk/Reward</h4>
        ${(data.rr || []).map(rr => {
            const qualityClass = rr.q === 'Tuyệt vời' ? 'excellent' : rr.q === 'Tốt' ? 'good' : 'fair';
            return `
                <div class="rr-card">
                    <div class="flex justify-between items-start mb-2">
                        <div>
                            <div class="text-xs text-muted-foreground">Mua: ${rr.bp}</div>
                            <div class="text-xs text-muted-foreground">SL: ${rr.slp}</div>
                            <div class="text-xs text-muted-foreground">TP: ${rr.tp}</div>
                        </div>
                        <div class="text-right">
                            <div class="rr-ratio">${rr.r}</div>
                            <span class="rr-quality ${qualityClass}">${rr.q}</span>
                        </div>
                    </div>
                </div>
            `;
        }).join('')}
    `;
}

// New function to render categorized indicators
async function renderCategorizedIndicators(symbol) {
    clearAllIndicatorsGlobally();

    if (!indicatorDataCache[symbol]) indicatorDataCache[symbol] = {};

    // Render indicators by category
    for (const [category, indicators] of Object.entries(CHART_CATEGORIES)) {
        const container = document.getElementById(`${category}Charts`);
        if (!container) continue;

        container.innerHTML = '';

        for (const indicator of indicators) {
            try {
                const indicatorPath = ANALYSIS_PATH + symbol + '_' + indicator + MAIN_SUFFIX;
                let indicatorData;

                if (indicatorDataCache[symbol][indicator]) {
                    indicatorData = indicatorDataCache[symbol][indicator];
                } else {
                    indicatorData = await fetchJSONWithCache(indicatorPath, {});
                    indicatorDataCache[symbol][indicator] = indicatorData;
                }

                // Create chart card
                const card = document.createElement('div');
                card.className = 'chart-container bg-card border border-border rounded-lg p-4';

                const title = document.createElement('div');
                title.className = 'font-semibold mb-2 text-center text-foreground';
                title.textContent = INDICATOR_LABELS[indicator] || indicator;

                const canvas = document.createElement('canvas');
                canvas.style.width = '100%';
                canvas.style.height = '300px';

                card.appendChild(title);
                card.appendChild(canvas);
                container.appendChild(card);

                // Render chart
                renderIndicatorChartToCanvas(indicator, indicatorData, canvas, `${symbol}_${indicator}`);
            } catch (e) {
                // No data for this indicator, skip
                console.warn(`No data available for ${indicator}`);
            }
        }
    }
}

// Function to initialize collapsible sections
function initializeCollapsibleSections() {
    // Load saved state
    const savedState = JSON.parse(localStorage.getItem(SECTIONS_STATE_KEY) || '{}');

    // Initialize all sections
    document.querySelectorAll('.chart-section').forEach(section => {
        const sectionId = section.dataset.section;
        const header = section.querySelector('.chart-section-header');

        // Apply saved state or default to expanded
        const isCollapsed = savedState[sectionId] === true;
        if (isCollapsed) {
            section.classList.add('collapsed');
        }

        // Add click handler
        header.addEventListener('click', () => {
            const wasCollapsed = section.classList.contains('collapsed');
            section.classList.toggle('collapsed');

            // Save state
            const currentState = JSON.parse(localStorage.getItem(SECTIONS_STATE_KEY) || '{}');
            currentState[sectionId] = !wasCollapsed;
            localStorage.setItem(SECTIONS_STATE_KEY, JSON.stringify(currentState));
        });
    });
}