let watchlistViewEl = null;
let createWatchlistDialogEl = null;
let createWatchlistDialogContentEl = null; // The inner dialog element with role="dialog"
let newWatchlistNameInputEl = null;
let confirmCreateWatchlistBtnEl = null;
let cancelCreateWatchlistBtnEl = null;
let searchAutocompleteEl = null;
let watchlistDialogTitleEl = null;
let watchlistNameErrorEl = null;
let symbolSearchInputEl = null;
let symbolSearchAutocompleteEl = null;
let selectedSymbolsContainerEl = null;

let watchlists = {}; // Format: { "WatchlistName1": ["SYM1", "SYM2"], "WatchlistName2": ["SYM3"] }
let selectedWatchlists = []; // Array of selected watchlist IDs
let currentSelectedWatchlistId = null; // The name of the currently selected watchlist
let isEditMode = false; // Whether we're editing an existing watchlist or creating a new one
let editingWatchlistId = null; // The ID of the watchlist being edited
let dialogSelectedSymbols = []; // Symbols selected in the dialog
const WATCHLIST_STORAGE_KEY = 'watchlists';
const SELECTED_WATCHLISTS_STORAGE_KEY = 'selectedWatchlists';

// Sample stock data - in a real app, this would come from an API
const SAMPLE_STOCKS = [
    { symbol: 'HPG', name: 'Hòa Phát Group', price: '21.55', change: '+0.55', changePercent: '*****%', trend: 'up' },
    { symbol: 'VNM', name: 'Vinamilk', price: '71.20', change: '-0.30', changePercent: '-0.42%', trend: 'down' },
    { symbol: 'TCB', name: 'Techcombank', price: '32.45', change: '+0.75', changePercent: '*****%', trend: 'up' },
    { symbol: 'VCB', name: 'Vietcombank', price: '89.90', change: '*****', changePercent: '*****%', trend: 'up' },
    { symbol: 'VIC', name: 'Vingroup', price: '43.25', change: '-0.65', changePercent: '-1.48%', trend: 'down' },
    { symbol: 'FPT', name: 'FPT Corp', price: '112.80', change: '*****', changePercent: '*****%', trend: 'up' },
    { symbol: 'MWG', name: 'Mobile World', price: '53.10', change: '-0.90', changePercent: '-1.67%', trend: 'down' },
    { symbol: 'VHM', name: 'Vinhomes', price: '47.35', change: '+0.25', changePercent: '+0.53%', trend: 'up' },
    { symbol: 'MSN', name: 'Masan Group', price: '74.60', change: '-1.40', changePercent: '-1.84%', trend: 'down' },
    { symbol: 'VRE', name: 'Vincom Retail', price: '25.75', change: '+0.45', changePercent: '*****%', trend: 'up' }
];

function loadWatchlists() {
    const storedWatchlists = localStorage.getItem(WATCHLIST_STORAGE_KEY);
    if (storedWatchlists) {
        watchlists = JSON.parse(storedWatchlists);
        const availableWatchlistIds = Object.keys(watchlists);
        if (availableWatchlistIds.length > 0) {
            // Try to keep currentSelectedWatchlistId if it's still valid, else default
            if (currentSelectedWatchlistId && watchlists.hasOwnProperty(currentSelectedWatchlistId)) {
                // It's still valid, do nothing
            } else {
                currentSelectedWatchlistId = availableWatchlistIds[0];
            }
        } else {
            currentSelectedWatchlistId = null;
        }
    } else {
        // Create some default watchlists for testing
        watchlists = {
            'Ngân hàng': ['TCB', 'VCB', 'BID', 'CTG'],
            'Bất động sản': ['VIC', 'VHM', 'VRE'],
            'Thép': ['HPG']
        };
        currentSelectedWatchlistId = 'Ngân hàng';
        saveWatchlists(); // Save the default watchlists
    }

    // Load selected watchlists from localStorage
    loadSelectedWatchlists();
}

function loadSelectedWatchlists() {
    const storedSelectedWatchlists = localStorage.getItem(SELECTED_WATCHLISTS_STORAGE_KEY);
    const availableWatchlistIds = Object.keys(watchlists);

    if (storedSelectedWatchlists) {
        // Load saved selection and filter out any watchlists that no longer exist
        const savedSelection = JSON.parse(storedSelectedWatchlists);
        selectedWatchlists = savedSelection.filter(id => availableWatchlistIds.includes(id));
    } else {
        // First visit: auto-select ALL available watchlists (but don't save this auto-selection)
        selectedWatchlists = [...availableWatchlistIds];
    }
}

function saveSelectedWatchlists() {
    localStorage.setItem(SELECTED_WATCHLISTS_STORAGE_KEY, JSON.stringify(selectedWatchlists));
}

function saveWatchlists() {
    localStorage.setItem(WATCHLIST_STORAGE_KEY, JSON.stringify(watchlists));
}

// Expose save function for watchlist manager
window.saveWatchlistsFromManager = function (newWatchlists) {
    watchlists = newWatchlists;
    saveWatchlists();

    // Update the current UI
    initWatchlistMultiSelect();
    renderWatchlistTable();
};

export function initWatchlist(element) {
    watchlistViewEl = element;
    loadWatchlists(); // Load watchlists from storage

    // Get dialog elements once during initialization
    createWatchlistDialogEl = document.getElementById('createWatchlistDialog');
    createWatchlistDialogContentEl = createWatchlistDialogEl?.querySelector('[role="dialog"]');
    newWatchlistNameInputEl = document.getElementById('newWatchlistNameInput');
    confirmCreateWatchlistBtnEl = document.getElementById('confirmCreateWatchlistBtn');
    cancelCreateWatchlistBtnEl = document.getElementById('cancelCreateWatchlistBtn');
    watchlistDialogTitleEl = document.getElementById('watchlistDialogTitle');
    watchlistNameErrorEl = document.getElementById('watchlistNameError');
    symbolSearchInputEl = document.getElementById('symbolSearchInput');
    symbolSearchAutocompleteEl = document.getElementById('symbolSearchAutocomplete');
    selectedSymbolsContainerEl = document.getElementById('selectedSymbolsContainer');

    if (confirmCreateWatchlistBtnEl) {
        confirmCreateWatchlistBtnEl.addEventListener('click', handleWatchlistSave);
    }
    if (cancelCreateWatchlistBtnEl) {
        cancelCreateWatchlistBtnEl.addEventListener('click', closeCreateWatchlistDialog);
    }

    // Initialize symbol search in dialog
    if (symbolSearchInputEl && symbolSearchAutocompleteEl) {
        initDialogSymbolSearch();
    }

    // Add input event to validate watchlist name
    if (newWatchlistNameInputEl && watchlistNameErrorEl) {
        newWatchlistNameInputEl.addEventListener('input', validateWatchlistName);
    }
}

function openCreateWatchlistDialog() {
    // Reset dialog state
    isEditMode = false;
    editingWatchlistId = null;
    dialogSelectedSymbols = [];

    // Update UI
    if (watchlistNameErrorEl) {
        watchlistNameErrorEl.classList.add('hidden');
    }
    if (selectedSymbolsContainerEl) {
        selectedSymbolsContainerEl.innerHTML = '';
    }

    // Show dialog
    if (createWatchlistDialogEl) {
        createWatchlistDialogEl.classList.remove('hidden');
        createWatchlistDialogEl.classList.add('flex');
    }
    if (createWatchlistDialogContentEl) {
        createWatchlistDialogContentEl.setAttribute('data-state', 'open');
    }
    if (newWatchlistNameInputEl) {
        newWatchlistNameInputEl.value = '';
        newWatchlistNameInputEl.focus();
    }
}

function openEditWatchlistDialog(watchlistId) {
    if (!watchlists[watchlistId]) return;

    // Set dialog state
    isEditMode = true;
    editingWatchlistId = watchlistId;
    dialogSelectedSymbols = [...watchlists[watchlistId]]; // Copy symbols from the watchlist

    // Update UI
    if (watchlistDialogTitleEl) {
        watchlistDialogTitleEl.textContent = 'Sửa danh sách theo dõi';
    }
    if (confirmCreateWatchlistBtnEl) {
        confirmCreateWatchlistBtnEl.textContent = 'Cập nhật';
    }
    if (watchlistNameErrorEl) {
        watchlistNameErrorEl.classList.add('hidden');
    }
    if (newWatchlistNameInputEl) {
        newWatchlistNameInputEl.value = watchlistId;
    }

    // Render selected symbols
    renderDialogSelectedSymbols();

    // Show dialog
    if (createWatchlistDialogEl) {
        createWatchlistDialogEl.classList.remove('hidden');
        createWatchlistDialogEl.classList.add('flex');
    }
    if (createWatchlistDialogContentEl) {
        createWatchlistDialogContentEl.setAttribute('data-state', 'open');
    }
}

function closeCreateWatchlistDialog() {
    if (createWatchlistDialogContentEl) {
        createWatchlistDialogContentEl.setAttribute('data-state', 'closed');
    }
    // Use a small timeout to allow the animation to play
    setTimeout(() => {
        if (createWatchlistDialogEl) {
            createWatchlistDialogEl.classList.add('hidden');
            createWatchlistDialogEl.classList.remove('flex');
        }
    }, 200);

    // Reset dialog state
    isEditMode = false;
    editingWatchlistId = null;
    dialogSelectedSymbols = [];

    // Clear inputs
    if (newWatchlistNameInputEl) newWatchlistNameInputEl.value = '';
    if (symbolSearchInputEl) symbolSearchInputEl.value = '';
    if (watchlistNameErrorEl) watchlistNameErrorEl.classList.add('hidden');
    if (selectedSymbolsContainerEl) selectedSymbolsContainerEl.innerHTML = '';
}

export function displayWatchlist() {
    if (watchlistViewEl) {
        watchlistViewEl.style.display = 'block';
        loadWatchlists(); // Ensure we have the latest from storage, e.g. if modified in another tab (though unlikely for this app)
        renderWatchlistInterface(); // This will set up everything including the table
    }
}

export function hideWatchlist() {
    if (watchlistViewEl) {
        watchlistViewEl.style.display = 'none';
    }
}

function validateWatchlistName() {
    if (!newWatchlistNameInputEl || !watchlistNameErrorEl) return true;

    const name = newWatchlistNameInputEl.value.trim();

    // Check if name is empty
    if (!name) {
        watchlistNameErrorEl.textContent = 'Hãy nhập tên cho danh sách theo dõi';
        watchlistNameErrorEl.classList.remove('hidden');
        return false;
    }

    // In edit mode, if the name hasn't changed, it's valid
    if (isEditMode && name === editingWatchlistId) {
        watchlistNameErrorEl.classList.add('hidden');
        return true;
    }

    // Check if name already exists
    if (watchlists.hasOwnProperty(name)) {
        watchlistNameErrorEl.textContent = 'Tên này đã được sử dụng';
        watchlistNameErrorEl.classList.remove('hidden');
        return false;
    }

    // Name is valid
    watchlistNameErrorEl.classList.add('hidden');
    return true;
}

function handleWatchlistSave() {
    if (!newWatchlistNameInputEl) return;

    // Validate watchlist name
    if (!validateWatchlistName()) {
        newWatchlistNameInputEl.focus();
        return;
    }

    const newName = newWatchlistNameInputEl.value.trim();

    if (isEditMode) {
        // Handle edit mode
        if (editingWatchlistId && editingWatchlistId !== newName) {
            // Name has changed, create a new watchlist with the new name
            watchlists[newName] = dialogSelectedSymbols;
            delete watchlists[editingWatchlistId];

            // Update selected watchlists if the edited one was selected
            const index = selectedWatchlists.indexOf(editingWatchlistId);
            if (index !== -1) {
                selectedWatchlists[index] = newName;
            }
        } else {
            // Just update the symbols
            watchlists[editingWatchlistId] = dialogSelectedSymbols;
        }
    } else {
        // Handle create mode
        watchlists[newName] = dialogSelectedSymbols;
        selectedWatchlists.push(newName); // Add to selected watchlists
    }

    // Save and update UI
    saveWatchlists();
    initWatchlistMultiSelect();
    renderWatchlistTable();

    // Close dialog
    closeCreateWatchlistDialog();
}

function openWatchlistManagerDialog() {
    console.log('Opening watchlist manager dialog...');
    // Import the watchlist manager and open it
    import('./watchlist-manager.js').then(module => {
        console.log('Watchlist manager module loaded');
        module.openWatchlistManager(watchlists);
    }).catch(error => {
        console.error('Failed to load watchlist manager:', error);
    });
}

function initWatchlistMultiSelect() {
    const watchlistDropdown = document.getElementById('watchlist-dropdown');
    if (!watchlistDropdown) return;

    const watchlistIds = Object.keys(watchlists);

    // Clear dropdown content
    watchlistDropdown.innerHTML = '';

    if (watchlistIds.length === 0) {
        const emptyItem = document.createElement('div');
        emptyItem.className = 'relative flex cursor-default select-none items-center rounded-sm py-2 px-2 text-sm outline-none text-muted-foreground';
        emptyItem.textContent = 'No watchlists available';
        watchlistDropdown.appendChild(emptyItem);
    } else {
        watchlistIds.forEach(id => {
            const item = document.createElement('div');
            item.className = 'flex justify-between cursor-default select-none items-center rounded-sm py-2 pl-2 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';
            item.dataset.value = id;

            // Create checkbox indicator
            const checkbox = document.createElement('button');
            checkbox.setAttribute('type', 'button');
            checkbox.setAttribute('role', 'checkbox');
            checkbox.setAttribute('aria-checked', 'false');
            checkbox.className = 'peer h-4 w-4 shrink-0 rounded-sm border border-primary shadow focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground';

            if (selectedWatchlists.includes(id)) {
                checkbox.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="14px" height="14px" viewBox="0 -960 960 960"fill="#000000"><path d="M382-253.85 168.62-467.23 211.38-510 382-339.38 748.62-706l42.76 42.77L382-253.85Z"/></svg>
                `
            }

            const checkSpan = document.createElement('span');
            checkSpan.className = 'flex h-3.5 w-3.5 items-center justify-center';
            checkSpan.appendChild(checkbox);
            item.appendChild(checkSpan);

            // Add text
            const textSpan = document.createElement('span');
            textSpan.textContent = id;

            // Left side with checkbox and text
            const leftSide = document.createElement('div');
            leftSide.className = 'flex items-center flex-1 ml-2';
            leftSide.appendChild(textSpan);
            item.appendChild(leftSide);

            // Right side with edit button
            const editButton = document.createElement('button');
            editButton.className = 'ml-2 text-muted-foreground hover:text-foreground';
            editButton.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-pencil">
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                    <path d="m15 5 4 4"></path>
                </svg>
            `;

            // Add click handlers
            checkbox.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleWatchlistSelection(id);
            });

            leftSide.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleWatchlistSelection(id);
            });

            editButton.addEventListener('click', (e) => {
                e.stopPropagation();
                openEditWatchlistDialog(id);
            });

            item.appendChild(editButton);
            watchlistDropdown.appendChild(item);
        });
    }

    // Update the selected watchlists display
    updateSelectedWatchlistsDisplay();
}

function toggleWatchlistSelection(watchlistId) {
    const index = selectedWatchlists.indexOf(watchlistId);

    if (index === -1) {
        // Add to selection
        selectedWatchlists.push(watchlistId);
    } else {
        // Remove from selection
        selectedWatchlists.splice(index, 1);
    }

    // Save user-initiated selection to localStorage
    saveSelectedWatchlists();

    // Update the display
    updateSelectedWatchlistsDisplay();

    // Re-render the dropdown to update checkmarks
    initWatchlistMultiSelect();

    // Re-render the table with filtered data
    renderWatchlistTable();
}

function updateSelectedWatchlistsDisplay() {
    const selectedContainer = document.getElementById('selected-watchlists');
    const placeholder = document.getElementById('watchlist-placeholder');

    if (!selectedContainer) return;

    // Clear previous badges except placeholder
    Array.from(selectedContainer.children).forEach(child => {
        if (child.id !== 'watchlist-placeholder') {
            child.remove();
        }
    });

    // Show/hide placeholder based on selection
    if (selectedWatchlists.length > 0) {
        placeholder?.classList.add('hidden');

        // If more than 2 watchlists are selected, show first one + count
        if (selectedWatchlists.length > 2) {
            // Add the first watchlist
            const firstId = selectedWatchlists[0];
            const firstBadge = document.createElement('span');
            firstBadge.className = 'inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground';

            const firstText = document.createElement('span');
            firstText.textContent = firstId;
            firstBadge.appendChild(firstText);

            const firstCloseBtn = document.createElement('button');
            firstCloseBtn.className = 'ml-1 rounded-full text-secondary-foreground hover:bg-secondary-foreground/20';
            firstCloseBtn.innerHTML = `
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                    <path d="M18 6 6 18"></path>
                    <path d="m6 6 12 12"></path>
                </svg>
            `;
            firstCloseBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                toggleWatchlistSelection(firstId);
            });

            firstBadge.appendChild(firstCloseBtn);
            selectedContainer.appendChild(firstBadge);

            // Add the count badge
            const countBadge = document.createElement('span');
            countBadge.className = 'inline-flex items-center rounded-md bg-primary px-2 py-1 text-xs font-medium text-primary-foreground';
            countBadge.textContent = `+${selectedWatchlists.length - 1}`;
            selectedContainer.appendChild(countBadge);
        } else {
            // Add badges for all selected watchlists (1 or 2)
            selectedWatchlists.forEach(id => {
                const badge = document.createElement('span');
                badge.className = 'inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground';

                const text = document.createElement('span');
                text.textContent = id;
                badge.appendChild(text);

                const closeBtn = document.createElement('button');
                closeBtn.className = 'ml-1 rounded-full text-secondary-foreground hover:bg-secondary-foreground/20';
                closeBtn.innerHTML = `
                    <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                    </svg>
                `;
                closeBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); // Prevent dropdown from toggling
                    toggleWatchlistSelection(id);
                });

                badge.appendChild(closeBtn);
                selectedContainer.appendChild(badge);
            });
        }
    } else {
        placeholder?.classList.remove('hidden');
    }
}

function toggleWatchlistDropdown() {
    const dropdown = document.getElementById('watchlist-dropdown');
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }
}

function initSearchAutocomplete() {
    const searchInput = document.getElementById('watchlist-search');
    const autocompleteDropdown = document.getElementById('search-autocomplete');

    if (!searchInput || !autocompleteDropdown) return;

    // Sample stock symbols for autocomplete
    const stockSymbols = SAMPLE_STOCKS.map(stock => stock.symbol);

    // Add input event listener
    searchInput.addEventListener('input', function () {
        const query = this.value.trim().toUpperCase();

        if (query.length === 0) {
            autocompleteDropdown.classList.add('hidden');
            return;
        }

        // Filter symbols based on input
        const filteredSymbols = stockSymbols.filter(symbol =>
            symbol.includes(query)
        );

        // Clear previous results
        autocompleteDropdown.innerHTML = '';

        if (filteredSymbols.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'py-6 text-center text-sm text-muted-foreground';
            noResults.textContent = 'No results found';
            autocompleteDropdown.appendChild(noResults);
        } else {
            filteredSymbols.forEach(symbol => {
                const item = document.createElement('div');
                item.value = symbol;
                item.className = 'relative flex cursor-default select-none items-center rounded-sm px-4 py-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';

                // Add text
                const textSpan = document.createElement('span');
                textSpan.textContent = symbol;
                item.appendChild(textSpan);

                item.addEventListener('click', () => {
                    searchInput.value = symbol;
                    autocompleteDropdown.classList.add('hidden');
                    addSymbolToSelectedWatchlists(symbol);
                });

                autocompleteDropdown.appendChild(item);
            });
        }

        // Show dropdown
        autocompleteDropdown.classList.remove('hidden');
    });

    // Show dropdown on focus if input has value
    searchInput.addEventListener('focus', function () {
        if (this.value.trim().length > 0) {
            const event = new Event('input');
            this.dispatchEvent(event);
        }
    });

    // Add keydown event for keyboard navigation
    searchInput.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && this.value.trim().length > 0) {
            e.preventDefault();
            addSymbolToSelectedWatchlists(this.value.trim().toUpperCase());
            this.value = '';
            autocompleteDropdown.classList.add('hidden');
        }
    });
}

// Helper function to check if a symbol is in any selected watchlist
function isSymbolInSelectedWatchlists(symbol) {
    if (selectedWatchlists.length === 0) return false;

    for (const watchlistId of selectedWatchlists) {
        if (watchlists[watchlistId] && watchlists[watchlistId].includes(symbol)) {
            return true;
        }
    }

    return false;
}

function addSymbolToSelectedWatchlists(symbol) {
    if (selectedWatchlists.length === 0) {
        alert('Please select at least one watchlist first.');
        return;
    }

    if (!/^[A-Z0-9]{1,10}$/.test(symbol)) {
        alert("Invalid symbol format. Please use 1-10 uppercase letters and numbers (e.g., AAPL, HPG).");
        return;
    }

    let addedToAny = false;

    selectedWatchlists.forEach(watchlistId => {
        if (!watchlists[watchlistId].includes(symbol)) {
            watchlists[watchlistId].push(symbol);
            watchlists[watchlistId].sort(); // Keep symbols sorted
            addedToAny = true;
        }
    });

    if (addedToAny) {
        saveWatchlists();
        renderWatchlistTable();
    } else {
        alert(`Symbol ${symbol} is already in all selected watchlists.`);
    }
}

function renderWatchlistTable() {
    const tableContainer = document.getElementById('watchlist-table-container');
    if (!tableContainer) return;

    // Get all symbols from selected watchlists
    let allSymbols = new Set();

    if (selectedWatchlists.length === 0) {
        tableContainer.innerHTML = `
            <div class="rounded-md bg-muted p-4 text-sm text-muted-foreground">
                <p>Hãy chọn ít nhất một danh sách theo dõi để xem cổ phiếu, hoặc tạo một danh sách mới.</p>
            </div>
        `;
        return;
    }

    // Collect all symbols from selected watchlists
    selectedWatchlists.forEach(watchlistId => {
        if (watchlists[watchlistId]) {
            watchlists[watchlistId].forEach(symbol => allSymbols.add(symbol));
        }
    });

    if (allSymbols.size === 0) {
        tableContainer.innerHTML = `
            <div class="rounded-md bg-muted p-4 text-sm text-muted-foreground">
                <p>Chưa có cổ phiếu nào trong danh sách theo dõi. Thêm cổ phiếu bằng cách sử dụng hộp tìm kiếm bên trên.</p>
            </div>
        `;
        return;
    }

    // Get stock data for the symbols
    const stocksToDisplay = [];
    allSymbols.forEach(symbol => {
        // Find the stock in our sample data
        const stockData = SAMPLE_STOCKS.find(stock => stock.symbol === symbol);
        if (stockData) {
            stocksToDisplay.push(stockData);
        } else {
            // If not found in sample data, create a basic entry
            stocksToDisplay.push({
                symbol: symbol,
                name: symbol,
                price: 'N/A',
                change: 'N/A',
                changePercent: 'N/A',
                trend: 'neutral'
            });
        }
    });

    // Create the enhanced table with better styling and visual hierarchy
    let tableHTML = `
        <div class="rounded-lg border border-border bg-card shadow-sm">
            <div class="relative w-full overflow-auto">
                <table class="w-full caption-bottom text-sm">
                    <thead class="[&_tr]:border-b bg-muted/30">
                        <tr class="border-b border-border transition-colors">
                            <th class="h-14 px-4 text-center align-middle font-semibold text-foreground">
                                <span class="material-symbols-outlined text-lg">image</span>
                            </th>
                            <th class="h-14 px-4 text-left align-middle font-semibold text-foreground">
                                <div class="flex items-center gap-2">
                                    <span class="material-symbols-outlined text-lg">trending_up</span>
                                    Mã CK
                                </div>
                            </th>
                            <th class="h-14 px-4 text-right align-middle font-semibold text-foreground">
                                <div class="flex items-center justify-end gap-2">
                                    <span class="material-symbols-outlined text-lg">attach_money</span>
                                    Giá hiện tại
                                </div>
                            </th>
                            <th class="h-14 px-4 text-left align-middle font-semibold text-foreground">
                                <div class="flex items-center gap-2">
                                    <span class="material-symbols-outlined text-lg">trending_up</span>
                                    Xu hướng & Độ mạnh
                                </div>
                            </th>
                            <th class="h-14 px-4 text-center align-middle font-semibold text-foreground">
                                <div class="flex items-center justify-center gap-2">
                                    <span class="material-symbols-outlined text-lg">verified</span>
                                    Tin cậy
                                </div>
                            </th>
                            <th class="h-14 px-4 text-center align-middle font-semibold text-foreground">
                                <div class="flex items-center justify-center gap-2">
                                    <span class="material-symbols-outlined text-lg">analytics</span>
                                    Phân tích kỹ thuật
                                </div>
                            </th>
                            <th class="h-14 px-4 text-left align-middle font-semibold text-foreground" style="min-width: 280px; max-width: 320px;">
                                <div class="flex items-center gap-2">
                                    <span class="material-symbols-outlined text-lg">lightbulb</span>
                                    Khuyến nghị & Chiến lược
                                </div>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="[&_tr:last-child]:border-0">
    `;

    stocksToDisplay.forEach(stock => {
        let trendClass = '';
        let trendIcon = 'trending_flat';
        if (stock.trend === 'up') {
            trendClass = 'text-green-600';
            trendIcon = 'trending_up';
        } else if (stock.trend === 'down') {
            trendClass = 'text-red-600';
            trendIcon = 'trending_down';
        }

        // Generate enhanced mock data with more realistic variations
        const mockTrendDirection = stock.trend === 'up' ? 'Tăng' : stock.trend === 'down' ? 'Giảm' : 'Trung tính';
        const mockStrength = stock.trend === 'up' ?
            ['Rất mạnh', 'Mạnh', 'Khá mạnh'][Math.floor(Math.random() * 3)] :
            stock.trend === 'down' ?
            ['Rất yếu', 'Yếu', 'Khá yếu'][Math.floor(Math.random() * 3)] :
            'Trung bình';

        const mockConfidence = stock.trend === 'up' ?
            ['85%', '78%', '72%'][Math.floor(Math.random() * 3)] :
            stock.trend === 'down' ?
            ['68%', '62%', '55%'][Math.floor(Math.random() * 3)] :
            ['52%', '48%', '45%'][Math.floor(Math.random() * 3)];

        // Enhanced recommendations with more variety
        const recommendations = {
            up: [
                'Khuyến nghị mua vào khi giá điều chỉnh về vùng hỗ trợ 25,200-25,500.',
                'Xu hướng tăng mạnh, có thể tích lũy thêm ở vùng 24,800-25,100.',
                'Breakout khỏi vùng kháng cự, mục tiêu ngắn hạn 26,500-27,000.',
                'Tín hiệu mua mạnh từ các chỉ báo kỹ thuật, SL đặt dưới 24,500.'
            ],
            down: [
                'Cần thận trọng, chờ tín hiệu đảo chiều tại vùng 24,000-24,300.',
                'Xu hướng giảm, nên chờ xác nhận đáy tại 23,500-23,800.',
                'Áp lực bán mạnh, tránh mua vào cho đến khi có tín hiệu phục hồi.',
                'Có thể short với SL trên 25,800, TP tại 23,200-23,500.'
            ],
            neutral: [
                'Thị trường sideway, theo dõi breakout khỏi vùng 24,500-25,500.',
                'Chờ tín hiệu rõ ràng hơn, hiện tại nên đứng ngoài quan sát.',
                'Tích lũy nhẹ ở vùng hỗ trợ, chờ xác nhận xu hướng mới.',
                'Dao động trong kênh, có thể trade ngắn hạn với R:R 1:2.'
            ]
        };

        const trendKey = stock.trend === 'up' ? 'up' : stock.trend === 'down' ? 'down' : 'neutral';
        const mockRecommendation = recommendations[trendKey][Math.floor(Math.random() * recommendations[trendKey].length)];

        // Generate random but consistent data for MA and Technical indicators
        const maTotal = Math.floor(Math.random() * 3) + 6; // 6-8 total indicators
        const maBuy = Math.floor(Math.random() * (maTotal - 2)) + 2; // At least 2 buy signals
        const maSell = Math.floor(Math.random() * (maTotal - maBuy)) + 1; // At least 1 sell signal
        const maNeutral = maTotal - maBuy - maSell;

        const techTotal = Math.floor(Math.random() * 4) + 8; // 8-11 total indicators
        const techBuy = Math.floor(Math.random() * (techTotal - 3)) + 2; // At least 2 buy signals
        const techSell = Math.floor(Math.random() * (techTotal - techBuy - 1)) + 1; // At least 1 sell signal
        const techNeutral = techTotal - techBuy - techSell;

        tableHTML += `
            <tr class="border-b border-border transition-all duration-200 hover:bg-muted/50 hover:shadow-sm cursor-pointer stock-row group" data-symbol="${stock.symbol}">
                <td class="p-4 align-middle text-center">
                    <div class="w-8 h-8 flex items-center justify-center">
                        <img src="./assets/${stock.symbol}.svg" alt="${stock.symbol}" class="w-6 h-6" onerror="this.style.display='none'; this.nextElementSibling.style.display='block';">
                        <span class="text-xs font-bold text-muted-foreground hidden">${stock.symbol.charAt(0)}</span>
                    </div>
                </td>
                <td class="p-4 align-middle">
                    <span class="font-bold text-base text-foreground">${stock.symbol}</span>
                </td>
                <td class="p-4 align-middle text-right">
                    <div class="flex flex-col items-end">
                        <span class="font-bold text-lg text-foreground">${stock.price}</span>
                        <span class="text-xs ${trendClass} font-medium">${stock.change} (${stock.changePercent})</span>
                    </div>
                </td>
                <td class="p-4 align-middle">
                    <div class="flex items-center gap-3">
                        <div class="w-8 h-8 rounded-full ${trendClass === 'text-green-600' ? 'bg-green-100' : trendClass === 'text-red-600' ? 'bg-red-100' : 'bg-gray-100'} flex items-center justify-center">
                            <span class="material-symbols-outlined text-sm ${trendClass}">${trendIcon}</span>
                        </div>
                        <div class="flex flex-col">
                            <span class="font-medium text-sm ${trendClass}">${mockTrendDirection}</span>
                            <span class="text-xs text-muted-foreground">${mockStrength}</span>
                        </div>
                    </div>
                </td>
                <td class="p-4 align-middle text-center">
                    <div class="flex flex-col items-center gap-1">
                        <div class="w-10 h-10 rounded-full bg-gradient-to-br from-yellow-100 to-orange-100 flex items-center justify-center border border-yellow-200">
                            <span class="font-bold text-sm text-yellow-700">${mockConfidence}</span>
                        </div>
                        <span class="text-xs text-muted-foreground">Độ tin cậy</span>
                    </div>
                </td>
                <td class="p-4 align-middle text-center">
                    <div class="flex flex-col items-center gap-3">
                        <!-- MA Signals -->
                        <div class="flex flex-col items-center gap-1">
                            <div class="flex items-center gap-1">
                                <span class="w-6 h-6 flex items-center justify-center rounded-full bg-gradient-to-br from-blue-100 to-blue-200 text-blue-800 font-bold text-xs border border-blue-300">${maTotal}</span>
                                <div class="flex rounded-md overflow-hidden border border-gray-200">
                                    <span class="px-1.5 py-0.5 bg-green-100 text-green-800 text-xs font-medium">${maBuy}</span>
                                    <span class="px-1.5 py-0.5 bg-red-100 text-red-800 text-xs font-medium">${maSell}</span>
                                    <span class="px-1.5 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium">${maNeutral}</span>
                                </div>
                            </div>
                            <span class="text-xs text-muted-foreground">MA Signals</span>
                        </div>

                        <!-- Technical Indicators -->
                        <div class="flex flex-col items-center gap-1">
                            <div class="flex items-center gap-1">
                                <span class="w-6 h-6 flex items-center justify-center rounded-full bg-gradient-to-br from-purple-100 to-purple-200 text-purple-800 font-bold text-xs border border-purple-300">${techTotal}</span>
                                <div class="flex rounded-md overflow-hidden border border-gray-200">
                                    <span class="px-1.5 py-0.5 bg-green-100 text-green-800 text-xs font-medium">${techBuy}</span>
                                    <span class="px-1.5 py-0.5 bg-red-100 text-red-800 text-xs font-medium">${techSell}</span>
                                    <span class="px-1.5 py-0.5 bg-gray-100 text-gray-800 text-xs font-medium">${techNeutral}</span>
                                </div>
                            </div>
                            <span class="text-xs text-muted-foreground">Technical</span>
                        </div>
                    </div>
                </td>
                <td class="p-4 align-middle" style="min-width: 280px; max-width: 320px;">
                    <div class="bg-muted/30 rounded-lg p-3 border border-border">
                        <div class="flex items-start gap-2">
                            <span class="material-symbols-outlined text-sm text-primary mt-0.5">lightbulb</span>
                            <div class="flex-1">
                                <p class="text-sm text-foreground leading-relaxed">${mockRecommendation}</p>
                                <div class="flex items-center gap-2 mt-2">
                                    <span class="px-2 py-1 rounded-full text-xs font-medium ${trendClass === 'text-green-600' ? 'bg-green-100 text-green-700' : trendClass === 'text-red-600' ? 'bg-red-100 text-red-700' : 'bg-gray-100 text-gray-700'}">
                                        ${stock.trend === 'up' ? 'Tích cực' : stock.trend === 'down' ? 'Thận trọng' : 'Quan sát'}
                                    </span>
                                    <span class="text-xs text-muted-foreground">• Cập nhật 2h trước</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        `;
    });

    tableHTML += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    tableContainer.innerHTML = tableHTML;

    // Add event listeners to stock rows for opening in new tabs
    tableContainer.querySelectorAll('.stock-row').forEach(row => {
        row.addEventListener('click', function (e) {
            const symbol = this.dataset.symbol;
            handleStockRowClick(symbol);
        });
    });
}

function handleStockRowClick(symbol) {
    // Import the necessary modules and functions
    import('./tabs.js').then(tabsModule => {
        import('./toast.js').then(toastModule => {
            // Get the tab list element
            const tabList = document.getElementById('selectedSymbolsPlaceholder');
            if (!tabList) {
                console.error('Tab list not found');
                return;
            }

            // Add the new tab for the stock
            const newTab = tabsModule.addTab(tabList, symbol, true, {
                afterSelected: (selectedSymbol) => {
                    // Hide watchlist and show stock detail view
                    hideWatchlist();
                    const stockDetailViewEl = document.getElementById('stockDetailView');
                    if (stockDetailViewEl) {
                        stockDetailViewEl.style.display = 'block';
                    }

                    // Load and render the symbol (this would need to be implemented)
                    // For now, we'll just show a placeholder
                    console.log(`Loading stock data for ${selectedSymbol}`);

                    // Import and call the stock loading function
                    import('./stock.js').then(stockModule => {
                        stockModule.loadAndRenderSymbol(selectedSymbol);
                    }).catch(error => {
                        console.error('Failed to load stock module:', error);
                    });
                }
            });

            // Select the new tab to make it active
            tabsModule.selectTab(tabList, newTab);

            // Update localStorage with the new selected symbols
            updateSelectedSymbolsInLocalStorage(symbol);

        }).catch(error => {
            console.error('Failed to load toast module:', error);
        });
    }).catch(error => {
        console.error('Failed to load tabs module:', error);
    });
}

function updateSelectedSymbolsInLocalStorage(newSymbol) {
    // Get current selected symbols from localStorage
    const currentSymbols = JSON.parse(localStorage.getItem('selectedSymbols') || '[]');

    // Add the new symbol if it's not already there
    if (!currentSymbols.includes(newSymbol)) {
        currentSymbols.push(newSymbol);
        localStorage.setItem('selectedSymbols', JSON.stringify(currentSymbols));
    }
}

function initDialogSymbolSearch() {
    if (!symbolSearchInputEl || !symbolSearchAutocompleteEl) return;

    // Sample stock symbols for autocomplete
    const stockSymbols = SAMPLE_STOCKS.map(stock => stock.symbol);

    // Add input event listener
    symbolSearchInputEl.addEventListener('input', function () {
        const query = this.value.trim().toUpperCase();

        if (query.length === 0) {
            symbolSearchAutocompleteEl.classList.add('hidden');
            return;
        }

        // Filter symbols based on input
        const filteredSymbols = stockSymbols.filter(symbol =>
            symbol.includes(query)
        );

        // Clear previous results
        symbolSearchAutocompleteEl.innerHTML = '';

        if (filteredSymbols.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'py-6 text-center text-sm text-muted-foreground';
            noResults.textContent = 'No results found';
            symbolSearchAutocompleteEl.appendChild(noResults);
        } else {
            filteredSymbols.forEach(symbol => {
                const item = document.createElement('div');
                item.className = 'relative flex cursor-default select-none items-center rounded-sm py-2 pl-8 pr-2 text-sm outline-none hover:bg-accent hover:text-accent-foreground';

                // Check if symbol is already selected in the dialog
                const isSymbolSelected = dialogSelectedSymbols.includes(symbol);

                // Create checkbox indicator for selected symbols
                const checkSpan = document.createElement('span');
                checkSpan.className = 'absolute left-2 flex h-3.5 w-3.5 items-center justify-center';

                if (isSymbolSelected) {
                    const checkIcon = document.createElement('svg');
                    checkIcon.setAttribute('xmlns', 'http://www.w3.org/2000/svg');
                    checkIcon.setAttribute('width', '16');
                    checkIcon.setAttribute('height', '16');
                    checkIcon.setAttribute('viewBox', '0 0 24 24');
                    checkIcon.setAttribute('fill', 'none');
                    checkIcon.setAttribute('stroke', 'currentColor');
                    checkIcon.setAttribute('stroke-width', '2');
                    checkIcon.setAttribute('stroke-linecap', 'round');
                    checkIcon.setAttribute('stroke-linejoin', 'round');
                    checkIcon.className = 'h-4 w-4';
                    checkIcon.innerHTML = '<polyline points="20 6 9 17 4 12"></polyline>';
                    checkSpan.appendChild(checkIcon);
                }

                item.appendChild(checkSpan);

                // Add text
                const textSpan = document.createElement('span');
                textSpan.textContent = symbol;
                item.appendChild(textSpan);

                item.addEventListener('click', () => {
                    toggleDialogSymbolSelection(symbol);
                    symbolSearchInputEl.value = '';
                    symbolSearchAutocompleteEl.classList.add('hidden');
                });

                symbolSearchAutocompleteEl.appendChild(item);
            });
        }

        // Show dropdown
        symbolSearchAutocompleteEl.classList.remove('hidden');
    });

    // Show dropdown on focus if input has value
    symbolSearchInputEl.addEventListener('focus', function () {
        if (this.value.trim().length > 0) {
            const event = new Event('input');
            this.dispatchEvent(event);
        }
    });

    // Add keydown event for keyboard navigation
    symbolSearchInputEl.addEventListener('keydown', function (e) {
        if (e.key === 'Enter' && this.value.trim().length > 0) {
            e.preventDefault();
            const symbol = this.value.trim().toUpperCase();
            toggleDialogSymbolSelection(symbol);
            this.value = '';
            symbolSearchAutocompleteEl.classList.add('hidden');
        }
    });
}

function toggleDialogSymbolSelection(symbol) {
    if (!symbol) return;

    const index = dialogSelectedSymbols.indexOf(symbol);

    if (index === -1) {
        // Add to selection
        dialogSelectedSymbols.push(symbol);
    } else {
        // Remove from selection
        dialogSelectedSymbols.splice(index, 1);
    }

    // Update the display
    renderDialogSelectedSymbols();
}

function renderDialogSelectedSymbols() {
    if (!selectedSymbolsContainerEl) return;

    // Clear previous badges
    selectedSymbolsContainerEl.innerHTML = '';

    if (dialogSelectedSymbols.length === 0) {
        const emptyMessage = document.createElement('span');
        emptyMessage.className = 'text-muted-foreground text-sm';
        emptyMessage.textContent = 'No symbols selected. Use the search box below to add symbols.';
        selectedSymbolsContainerEl.appendChild(emptyMessage);
        return;
    }

    // Sort symbols alphabetically
    const sortedSymbols = [...dialogSelectedSymbols].sort();

    // Add badges for selected symbols
    sortedSymbols.forEach(symbol => {
        const badge = document.createElement('span');
        badge.className = 'inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground';

        const text = document.createElement('span');
        text.textContent = symbol;
        badge.appendChild(text);

        const closeBtn = document.createElement('button');
        closeBtn.className = 'ml-1 rounded-full text-secondary-foreground hover:bg-secondary-foreground/20';
        closeBtn.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-x">
                <path d="M18 6 6 18"></path>
                <path d="m6 6 12 12"></path>
            </svg>
        `;
        closeBtn.addEventListener('click', () => {
            toggleDialogSymbolSelection(symbol);
        });

        badge.appendChild(closeBtn);
        selectedSymbolsContainerEl.appendChild(badge);
    });
}

function handleRemoveSymbolFromWatchlist(symbolToRemove) {
    if (selectedWatchlists.length === 0) return;

    // Remove the symbol from all selected watchlists
    selectedWatchlists.forEach(watchlistId => {
        if (watchlists[watchlistId]) {
            watchlists[watchlistId] = watchlists[watchlistId].filter(s => s !== symbolToRemove);
        }
    });

    saveWatchlists();
    renderWatchlistTable(); // Re-render the table
}

function renderWatchlistInterface() {
    if (!watchlistViewEl) return;

    // Clear previous content and set up watchlist UI with shadcn-ui styling
    watchlistViewEl.innerHTML = `
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-xl font-semibold">Danh sách theo dõi</h2>
            <div class="flex gap-2">
                <button id="view-watchlists-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 h-9 px-3">
                    <svg xmlns="http://www.w3.org/2000/svg" height="24px" viewBox="0 -960 960 960" width="24px" fill="#FFFFFF"><path d="M180-100v-530.77q0-29.92 21.19-51.11 21.2-21.2 51.12-21.2h301.15q29.92 0 51.12 21.2 21.19 21.19 21.19 51.11V-100L403.08-213.08 180-100Zm60-92.54 163.08-86.38 162.69 86.38v-438.23q0-5.38-3.46-8.85-3.46-3.46-8.85-3.46H252.31q-5.39 0-8.85 3.46-3.46 3.47-3.46 8.85v438.23Zm480-58.61v-536.54q0-5.39-3.46-8.85t-8.85-3.46H295.77v-60h411.92q29.92 0 51.12 21.19Q780-817.61 780-787.69v536.54h-60ZM240-643.08h325.77H240Z"/></svg>
                    Danh sách
                </button>
                <button id="create-watchlist-btn" class="inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium h-9 px-3 bg-secondary text-secondary-foreground hover:bg-secondary/90 disabled:pointer-events-none disabled:opacity-50">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-plus">
                        <path d="M12 5v14M5 12h14"></path>
                    </svg>
                    Tạo mới
                </button>
            </div>
        </div>

        <div class="mb-6">
            <div class="flex gap-4">
                <!-- Multi-select dropdown for watchlists -->
                <div class="relative min-w-[200px]">
                    <div id="watchlist-multi-select" class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm">
                        <div id="selected-watchlists" class="flex flex-wrap gap-1 overflow-hidden">
                            <!-- Selected watchlists will be rendered here -->
                            <span class="text-muted-foreground" id="watchlist-placeholder">Chọn danh sách theo dõi</span>
                        </div>
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 ml-1 opacity-50">
                            <path d="m6 9 6 6 6-6"></path>
                        </svg>
                    </div>
                    <div id="watchlist-dropdown" class="absolute z-50 hidden max-w-[150%] min-w-[70%] rounded-md border bg-background p-1 text-foreground shadow-md">
                        <!-- Watchlist options will be rendered here -->
                    </div>
                </div>

                <!-- Search input with autocomplete -->
                <div class="relative">
                    <input type="text" id="watchlist-search" placeholder="Tìm cổ phiếu"
                        class="flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm">
                    <div id="search-autocomplete" class="absolute z-50 hidden w-full min-w-[8rem] overflow-hidden rounded-md border bg-background p-1 text-foreground shadow-md">
                        <!-- Autocomplete options will be rendered here -->
                    </div>
                </div>
            </div>
        </div>

        <div id="watchlist-table-container" class="overflow-x-auto flex-1" style="height: calc(100vh - 200px);">
            <!-- Table will be rendered here by renderWatchlistTable -->
        </div>
    `;

    // Initialize the multi-select dropdown
    initWatchlistMultiSelect();

    // Initialize search autocomplete
    initSearchAutocomplete();

    // Render the table with stock data
    renderWatchlistTable();

    // Add event listeners for controls
    const createBtn = document.getElementById('create-watchlist-btn');
    if (createBtn) {
        createBtn.addEventListener('click', openCreateWatchlistDialog);
    }

    // Add event listener for watchlist manager button
    const viewWatchlistsBtn = document.getElementById('view-watchlists-btn');
    if (viewWatchlistsBtn) {
        console.log('Adding event listener to view-watchlists-btn');
        viewWatchlistsBtn.addEventListener('click', openWatchlistManagerDialog);
    } else {
        console.error('view-watchlists-btn not found');
    }

    // Toggle watchlist dropdown when clicking on the select
    const watchlistSelect = document.getElementById('watchlist-multi-select');
    if (watchlistSelect) {
        watchlistSelect.addEventListener('click', toggleWatchlistDropdown);
    }

    // Close dropdowns when clicking outside
    document.addEventListener('click', function (event) {
        const watchlistDropdown = document.getElementById('watchlist-dropdown');
        const watchlistSelect = document.getElementById('watchlist-multi-select');
        const searchAutocomplete = document.getElementById('search-autocomplete');
        const searchInput = document.getElementById('watchlist-search');

        if (watchlistDropdown && watchlistSelect &&
            !watchlistSelect.contains(event.target) &&
            !watchlistDropdown.contains(event.target)) {
            watchlistDropdown.classList.add('hidden');
        }

        if (searchAutocomplete && searchInput &&
            !searchInput.contains(event.target) &&
            !searchAutocomplete.contains(event.target)) {
            searchAutocomplete.classList.add('hidden');
        }
    });
}